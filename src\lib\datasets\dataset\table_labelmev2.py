"""
TableLabelMe格式的数据集类基础框架实现 - 迭代5步骤5.1

该数据集类实现TableLabelMe格式数据的基础框架，集成迭代1-4的所有组件，
确保与现有LORE-TSR训练流程完全兼容。

迭代5步骤5.1的核心目标：
1. 创建完整的TableLabelMe数据集类基础框架
2. 集成迭代1-4的所有组件
3. 实现__init__和__len__方法
4. 实现基础的文件索引构建和质量筛选流程
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import numpy as np
import json
import random
import os
import hashlib
import cv2
from typing import Dict, Any, List, Optional, Union

import torch.utils.data as data

from ..parsers import TableLabelMeParser, FileScanner, QualityFilter
from ...utils.logger_config import LoggerConfig
from ...utils.image import get_affine_transform, affine_transform, color_aug


class Table(data.Dataset):
    """
    TableLabelMe格式数据集类基础框架 - 迭代5步骤5.1实现

    该类负责加载TableLabelMe格式的数据，集成迭代1-4的所有组件，
    并将其转换为与LORE-TSR训练流程兼容的标准格式。

    核心功能：
    1. 集成TableLabelMeParser（迭代1）
    2. 集成FileScanner（迭代2）
    3. 集成QualityFilter（迭代3）
    4. 集成ConfigLoader（迭代4）
    5. 实现基础的文件索引构建和质量筛选流程
    """

    # 类属性，与原Table类保持一致，确保兼容性
    num_classes = 2
    table_size = 1024
    default_resolution = [1024, 1024]
    mean = np.array([0.40789654, 0.44719302, 0.47026115],
                   dtype=np.float32).reshape(1, 1, 3)
    std = np.array([0.28863828, 0.27408164, 0.27809835],
                  dtype=np.float32).reshape(1, 1, 3)

    def __init__(self, opt, split: str):
        """
        初始化TableLabelMe数据集基础框架，集成迭代1-4的所有组件。

        Args:
            opt: 配置对象，包含数据路径和训练参数
            split (str): 数据集分割（train/val/test）

        Note:
            严格按照迭代5步骤5.1的要求实现：
            1. 实现基础类结构，继承现有Dataset基类
            2. 集成迭代1-4的所有组件
            3. 实现__init__方法，初始化所有组件
            4. 实现基础的文件索引构建和质量筛选流程
        """
        super(Table, self).__init__(opt, split)
        # super(Table, self).__init__()
        self.split = split
        self.opt = opt

        # 基本参数设置，与原Table类保持一致，确保兼容性
        self.max_objs = 300
        self.max_pairs = 900
        self.max_cors = 1200

        self.class_name = ['__background__', 'center', 'corner']
        self._valid_ids = [1, 2]
        self.cat_ids = {v: i for i, v in enumerate(self._valid_ids)}
        self.voc_color = [(v // 32 * 64 + 64, (v // 8) % 4 * 64, v % 8 * 32)
                         for v in range(1, self.num_classes + 1)]
        self._data_rng = np.random.RandomState(123)
        self._eig_val = np.array([0.2141788, 0.01817699, 0.00341571],
                                dtype=np.float32)
        self._eig_vec = np.array([
            [-0.58752847, -0.69563484, 0.41340352],
            [-0.5832747, 0.00994535, -0.81221408],
            [-0.56089297, 0.71832671, 0.41158938]
        ], dtype=np.float32)

        # 设置日志记录器
        self.logger = LoggerConfig.setup_logger(f"TableLabelMe.{split}")
        self.logger.info(f'==> 开始初始化TableLabelMe {split} 数据集基础框架')

        # 步骤1：初始化TableLabelMe解析器（集成迭代1成果）
        self.parser = TableLabelMeParser()
        self.logger.info('✓ TableLabelMeParser初始化完成（迭代1）')

        # 步骤2：初始化文件扫描器（集成迭代2成果）
        self.file_scanner = FileScanner()
        self.logger.info('✓ FileScanner初始化完成（迭代2）')

        # 步骤3：创建质量筛选器（集成迭代3成果）
        quality_config = getattr(opt, 'quality_filter_config', None)
        self.quality_filter = QualityFilter(config=quality_config, logger=self.logger)
        self.logger.info('✓ QualityFilter初始化完成（迭代3）')

        # 步骤4：集成配置系统（集成迭代4成果）
        self._integrate_config_system()
        self.logger.info('✓ 配置系统集成完成（迭代4）')

        # 步骤5：构建文件索引（集成质量筛选）
        self.file_index = self._build_file_index()
        self.logger.info(f'✓ 文件索引构建完成，发现 {len(self.file_index)} 个有效文件对')

        # 步骤6：加载标注数据
        self._load_annotations()

        # 步骤7：完成初始化
        self.num_samples = len(self.images)
        self.logger.info(f'✓ TableLabelMe数据集基础框架初始化完成 - {split}: {self.num_samples}个样本')

        self.coco = self
        self.img_dir = ""

    def _integrate_config_system(self) -> None:
        """
        集成配置系统（迭代4成果），支持TableLabelMe格式的配置加载。

        该方法集成迭代4的配置系统成果，专注于步骤5.1的基础框架需求：
        - 检测配置模式
        - 保存配置信息
        - 设置数据路径

        Note:
            步骤5.1专注于基础框架，复杂的配置功能在后续步骤中实现。
        """
        try:
            self.logger.info("集成配置系统（迭代4成果）")

            # 检查配置系统参数
            dataset_mode = getattr(self.opt, 'dataset_mode', 'COCO')
            config_data = getattr(self.opt, 'config_data', None)
            data_paths = getattr(self.opt, 'data_paths', {})

            self.logger.info(f"数据集模式: {dataset_mode}")

            if dataset_mode == 'TableLabelMe':
                self.logger.info("TableLabelMe模式，集成外部配置")

                # 保存配置信息
                self._config_data = config_data
                self._external_data_paths = data_paths

                # 记录配置状态
                if config_data:
                    self.logger.info(f"配置数据: {config_data.get('description', '无描述')}")
                if data_paths and self.split in data_paths:
                    path_count = len(data_paths[self.split])
                    self.logger.info(f"外部数据路径: {path_count}个路径")

            else:
                self.logger.info("COCO兼容模式，使用默认配置")
                self._config_data = None
                self._external_data_paths = {}

            self.logger.info("配置系统集成完成")

        except Exception as e:
            self.logger.error(f"配置系统集成失败: {e}")
            # 失败时使用默认配置
            self._config_data = None
            self._external_data_paths = {}

    def _get_data_paths(self) -> List[str]:
        """
        获取数据路径配置，集成迭代4的配置系统成果。

        Returns:
            List[str]: 数据路径列表

        Note:
            步骤5.1专注于基础框架，优先级：外部配置 > 硬编码路径
        """
        # 检查外部配置路径（迭代4成果）
        external_paths = getattr(self, '_external_data_paths', {})
        if external_paths and self.split in external_paths:
            configured_paths = external_paths[self.split]
            if configured_paths:
                self.logger.debug(f"使用外部配置路径: {len(configured_paths)}个")
                return configured_paths

        # 使用更新后的真实数据路径
        train_path = "D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/lore_test_data/train/"
        val_path = "D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/lore_test_data/val/"

        default_paths = {
            'train': [train_path],
            'val': [val_path]
        }

        result_paths = default_paths.get(self.split, [])
        self.logger.debug(f"使用默认路径: {len(result_paths)}个")
        return result_paths


    def _build_file_index(self) -> Dict[int, Dict[str, str]]:
        """
        构建文件索引并进行质量筛选（集成迭代2和迭代3成果）。

        Returns:
            Dict[int, Dict[str, str]]: 经过质量筛选的文件映射字典

        Note:
            步骤5.1的核心功能：
            1. 使用FileScanner进行目录扫描（迭代2成果）
            2. 使用QualityFilter进行质量筛选（迭代3成果）
            3. 构建完整的文件索引
        """
        try:
            self.logger.info("开始构建文件索引（集成迭代2和迭代3成果）")

            # 获取数据路径配置
            data_paths = self._get_data_paths()

            if not data_paths:
                self.logger.warning(f'未找到{self.split}分割的数据路径配置')
                return {}

            # 步骤1：使用FileScanner进行目录扫描（迭代2成果）
            self.logger.info("执行目录扫描（FileScanner - 迭代2）")
            scan_result = self.file_scanner.scan_directories(data_paths, self.split)

            # 保存扫描统计信息
            self.scan_statistics = scan_result.get('statistics', {})
            raw_file_index = scan_result.get('file_index', {})

            self.logger.info(f'目录扫描完成，发现 {len(raw_file_index)} 个文件对')
            if hasattr(self, 'scan_statistics'):
                stats = self.scan_statistics
                self.logger.info(f'扫描统计：{stats.get("valid_pairs", 0)}个有效对，'
                              f'{stats.get("orphan_images", 0)}个孤儿图像，'
                              f'扫描时间：{stats.get("scan_time", 0):.3f}秒')

            # 步骤2：使用QualityFilter进行质量筛选（迭代3成果）
            self.logger.info("执行质量筛选（QualityFilter - 迭代3）")
            filter_result = self.quality_filter.filter_samples(raw_file_index, self.split)

            # 保存筛选结果和统计信息
            self.filter_statistics = filter_result["statistics"]
            self.exception_report = filter_result["exception_report"]

            # 记录筛选结果
            stats = self.filter_statistics
            self.logger.info(f'质量筛选完成 - 有效: {stats["valid_samples"]}, '
                            f'筛选掉: {stats["filtered_samples"]}, '
                            f'错误: {stats["error_samples"]}, '
                            f'成功率: {self.exception_report["summary"]["success_rate"]:.1f}%')

            return filter_result["filtered_index"]

        except Exception as e:
            self.logger.error(f'文件索引构建失败：{e}')
            self.logger.info('回退到空索引')
            # 保存错误信息到统计中
            self.scan_statistics = {
                'error': str(e),
                'valid_pairs': 0,
                'total_images': 0,
                'total_annotations': 0
            }
            self.filter_statistics = {
                'total_processed': 0,
                'valid_samples': 0,
                'filtered_samples': 0,
                'error_samples': 0
            }
            self.exception_report = {
                'summary': {'success_rate': 0.0}
            }
            return {}

    def _load_annotations(self) -> None:
        """
        加载并解析TableLabelMe标注文件（步骤5.1基础实现）。

        Note:
            步骤5.1专注于基础框架，使用简化的标注加载逻辑。
            将TableLabelMe格式转换为与COCO兼容的数据结构，
            确保与现有训练流程完全兼容。
        """
        try:
            self.logger.info("开始加载标注数据")

            self.images = []
            self.annotations = {}
            self.image_info = {}

            # 遍历文件索引，加载标注数据
            for image_id, file_info in self.file_index.items():
                try:
                    # 步骤5.1：使用基础的标注加载逻辑
                    # 后续步骤将实现完整的TableLabelMe解析

                    # 创建图像信息（与COCO格式兼容）
                    self.image_info[image_id] = {
                        'id': image_id,
                        'file_name': file_info['image_path'],
                        'width': 1024,  # 默认尺寸，后续步骤将从实际图像获取
                        'height': 768
                    }

                    # 创建基础标注数据（与COCO格式兼容）
                    basic_annotations = self._create_basic_annotations(image_id, file_info)
                    self.annotations[image_id] = basic_annotations

                    self.images.append(image_id)

                except Exception as e:
                    self.logger.warning(f"跳过无效样本 {image_id}: {e}")
                    continue

            self.logger.info(f"标注数据加载完成，有效样本: {len(self.images)}个")

            # 数据验证和边界检查（Bug修复）
            self._validate_logic_axis_data()

        except Exception as e:
            self.logger.error(f"标注数据加载失败: {e}")
            # 确保基础数据结构存在
            self.images = []
            self.annotations = {}
            self.image_info = {}

    def _validate_logic_axis_data(self) -> None:
        """
        验证logic_axis数据的合理性，检测可能导致整数溢出的异常值（Bug修复）。

        该方法在数据加载完成后执行全面的数据验证，确保：
        1. logic_axis数值在安全范围内
        2. 逻辑坐标的一致性
        3. 异常数据的识别和报告
        """
        try:
            self.logger.info("🔍 开始验证logic_axis数据（Bug修复）")

            # 统计信息
            total_annotations = 0
            overflow_count = 0
            unreasonable_count = 0
            inconsistent_count = 0
            problematic_images = []

            # 定义检查范围
            MIN_SAFE_VALUE = -2147483648  # int32最小值
            MAX_SAFE_VALUE = 2147483647   # int32最大值
            MIN_LOGICAL_VALUE = 0         # 逻辑坐标最小值
            MAX_LOGICAL_VALUE = 10000     # 逻辑坐标最大值

            # 遍历所有标注进行验证
            for image_id in self.images:
                annotations = self.annotations.get(image_id, [])
                image_has_problem = False

                for ann_idx, ann in enumerate(annotations):
                    total_annotations += 1

                    if 'logic_axis' not in ann:
                        continue

                    logic_axis = ann['logic_axis']
                    if not logic_axis or len(logic_axis) == 0:
                        continue

                    # 提取logic_axis值
                    try:
                        if isinstance(logic_axis[0], list):
                            values = logic_axis[0]
                        else:
                            values = logic_axis

                        if len(values) != 4:
                            self.logger.warning(f"⚠️  logic_axis长度异常: img_id={image_id}, "
                                              f"ann_idx={ann_idx}, length={len(values)}")
                            continue

                        start_row, end_row, start_col, end_col = values

                        # 检查整数溢出风险
                        for i, (name, value) in enumerate([
                            ('start_row', start_row), ('end_row', end_row),
                            ('start_col', start_col), ('end_col', end_col)
                        ]):
                            try:
                                float_val = float(value)
                                if float_val < MIN_SAFE_VALUE or float_val > MAX_SAFE_VALUE:
                                    overflow_count += 1
                                    image_has_problem = True
                                    self.logger.error(f"🚨 整数溢出风险: img_id={image_id}, "
                                                    f"ann_idx={ann_idx}, {name}={float_val}")

                                # 检查合理性
                                if float_val < MIN_LOGICAL_VALUE or float_val > MAX_LOGICAL_VALUE:
                                    unreasonable_count += 1
                                    image_has_problem = True
                                    self.logger.warning(f"⚠️  不合理数值: img_id={image_id}, "
                                                      f"ann_idx={ann_idx}, {name}={float_val}")

                            except (ValueError, TypeError):
                                self.logger.error(f"❌ 数值转换失败: img_id={image_id}, "
                                                f"ann_idx={ann_idx}, {name}={value}")
                                image_has_problem = True

                        # 检查逻辑一致性
                        try:
                            if float(start_row) > float(end_row):
                                inconsistent_count += 1
                                image_has_problem = True
                                self.logger.warning(f"⚠️  行坐标不一致: img_id={image_id}, "
                                                  f"start_row={start_row} > end_row={end_row}")

                            if float(start_col) > float(end_col):
                                inconsistent_count += 1
                                image_has_problem = True
                                self.logger.warning(f"⚠️  列坐标不一致: img_id={image_id}, "
                                                  f"start_col={start_col} > end_col={end_col}")
                        except (ValueError, TypeError):
                            pass

                    except Exception as e:
                        self.logger.error(f"❌ logic_axis验证异常: img_id={image_id}, "
                                        f"ann_idx={ann_idx}, error={e}")
                        image_has_problem = True

                if image_has_problem:
                    problematic_images.append(image_id)

            # 生成验证报告
            self.logger.info(f"📊 logic_axis数据验证完成:")
            self.logger.info(f"   总标注数: {total_annotations}")
            self.logger.info(f"   整数溢出风险: {overflow_count}")
            self.logger.info(f"   不合理数值: {unreasonable_count}")
            self.logger.info(f"   逻辑不一致: {inconsistent_count}")
            self.logger.info(f"   问题图像数: {len(problematic_images)}")

            if overflow_count > 0:
                self.logger.error(f"🚨 发现 {overflow_count} 个整数溢出风险，这可能是导致训练错误的原因！")
                self.logger.error(f"🚨 问题图像ID: {problematic_images[:10]}...")  # 只显示前10个

            if unreasonable_count > 0:
                self.logger.warning(f"⚠️  发现 {unreasonable_count} 个不合理数值，建议检查数据质量")

            # 保存验证结果供后续使用
            self.validation_report = {
                'total_annotations': total_annotations,
                'overflow_count': overflow_count,
                'unreasonable_count': unreasonable_count,
                'inconsistent_count': inconsistent_count,
                'problematic_images': problematic_images
            }

        except Exception as e:
            self.logger.error(f"❌ logic_axis数据验证失败: {e}")
            self.validation_report = {'error': str(e)}

    def _create_basic_annotations(self, image_id: int, file_info: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        创建标注数据（步骤5.2升级版：使用真实TableLabelMe解析和转换）。

        Args:
            image_id (int): 图像ID
            file_info (Dict[str, str]): 文件信息字典，包含image_path和annotation_path

        Returns:
            List[Dict[str, Any]]: 转换后的LORE-TSR格式标注数据列表

        Note:
            步骤5.2升级：使用真实的TableLabelMe解析和转换逻辑，
            调用TableLabelMeParser解析文件，然后使用_convert_to_lore_format转换。
        """
        try:
            # 步骤5.2：使用真实的TableLabelMe解析和转换逻辑
            self.logger.debug(f"开始解析标注文件: {file_info['annotation_path']}")

            # 步骤1：使用TableLabelMeParser解析文件
            parsed_annotations = self.parser.parse_file(
                file_info['annotation_path'],
                file_info['image_path']
            )

            if parsed_annotations is None:
                self.logger.warning(f"解析失败: {file_info['annotation_path']}")
                return []

            # 步骤2：转换每个解析后的标注
            converted_annotations = []
            for parsed_ann in parsed_annotations:
                try:
                    # 准备转换数据
                    conversion_data = {
                        'bbox': self._extract_bbox_from_parsed(parsed_ann),
                        'lloc': self._extract_lloc_from_parsed(parsed_ann),
                        'cell_ind': parsed_ann.get('annotation_id', 1),
                        'image_path': file_info['image_path'],
                        'annotation_path': file_info['annotation_path'],
                        'table_ind': parsed_ann.get('extra_info', {}).get('table_ind', 0),
                        'type': parsed_ann.get('extra_info', {}).get('type', 'cell'),
                        'border': parsed_ann.get('extra_info', {}).get('border', True),
                        'content': parsed_ann.get('extra_info', {}).get('content', ''),
                        'quality': parsed_ann.get('extra_info', {}).get('quality', '合格'),
                        'part_dir': file_info.get('part_dir', ''),
                        'dataset_source': file_info.get('dataset_source', 'TableLabelMe')
                    }

                    # 步骤3：使用_convert_to_lore_format进行转换
                    converted_ann = self._convert_to_lore_format(conversion_data)
                    if converted_ann is not None:
                        converted_annotations.append(converted_ann)

                except Exception as e:
                    self.logger.warning(f"单个标注转换失败: {e}")
                    continue

            self.logger.debug(f"成功转换 {len(converted_annotations)} 个标注")
            return converted_annotations

        except Exception as e:
            self.logger.warning(f"标注解析和转换失败 {image_id}: {e}")
            # 回退到基础标注（保持兼容性）
            return self._create_fallback_annotation(image_id, file_info)

    def _extract_bbox_from_parsed(self, parsed_ann: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """
        从解析后的标注中提取bbox数据。

        Args:
            parsed_ann (Dict[str, Any]): 解析后的标注数据

        Returns:
            Dict: bbox格式数据
        """
        try:
            # TableLabelMeParser已经将segmentation转换为标准格式
            # 我们需要将其转换回bbox格式以供_convert_to_lore_format使用
            segmentation = parsed_ann.get('segmentation', [])
            if len(segmentation) == 8:
                return {
                    'p1': {'x': segmentation[0], 'y': segmentation[1]},
                    'p2': {'x': segmentation[2], 'y': segmentation[3]},
                    'p3': {'x': segmentation[4], 'y': segmentation[5]},
                    'p4': {'x': segmentation[6], 'y': segmentation[7]}
                }
            else:
                # 默认bbox
                return {
                    'p1': {'x': 100.0, 'y': 50.0},
                    'p2': {'x': 200.0, 'y': 50.0},
                    'p3': {'x': 200.0, 'y': 100.0},
                    'p4': {'x': 100.0, 'y': 100.0}
                }
        except Exception:
            # 默认bbox
            return {
                'p1': {'x': 100.0, 'y': 50.0},
                'p2': {'x': 200.0, 'y': 50.0},
                'p3': {'x': 200.0, 'y': 100.0},
                'p4': {'x': 100.0, 'y': 100.0}
            }

    def _extract_lloc_from_parsed(self, parsed_ann: Dict[str, Any]) -> Dict[str, int]:
        """
        从解析后的标注中提取lloc数据。

        Args:
            parsed_ann (Dict[str, Any]): 解析后的标注数据

        Returns:
            Dict: lloc格式数据
        """
        try:
            # TableLabelMeParser已经将logic_axis转换为标准格式
            logic_axis = parsed_ann.get('logic_axis', [[0, 0, 0, 1]])
            if len(logic_axis) == 4:
                return {
                    'start_row': logic_axis[0],
                    'end_row': logic_axis[1],
                    'start_col': logic_axis[2],
                    'end_col': logic_axis[3]
                }
            else:
                # 默认lloc
                return {
                    'start_row': 0,
                    'end_row': 0,
                    'start_col': 0,
                    'end_col': 1
                }
        except Exception:
            # 默认lloc
            return {
                'start_row': 0,
                'end_row': 0,
                'start_col': 0,
                'end_col': 1
            }

    def _create_fallback_annotation(self, image_id: int, file_info: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        创建回退标注数据（当真实解析失败时使用）。

        Args:
            image_id (int): 图像ID
            file_info (Dict[str, str]): 文件信息字典

        Returns:
            List[Dict[str, Any]]: 回退标注数据列表
        """
        try:
            # 生成唯一的标注ID
            annotation_id = self._generate_annotation_id(image_id, 1)

            # 创建与COCO格式兼容的回退标注
            fallback_annotation = {
                'id': annotation_id,
                'image_id': image_id,
                'category_id': 1,  # 固定为1（单元格类别）
                'segmentation': [[100.0, 50.0, 200.0, 50.0, 200.0, 100.0, 100.0, 100.0]],  # 基础四边形
                'logic_axis': [[0, 0, 0, 1]],  # 基础逻辑坐标
                'area': 5000.0,  # 基础面积
                'bbox': [100.0, 50.0, 100.0, 50.0],  # 基础边界框
                'iscrowd': 0,
                'ignore': 0,
                'extra_info': {
                    'annotation_path': file_info['annotation_path'],
                    'part_dir': file_info.get('part_dir', ''),
                    'dataset_source': file_info.get('dataset_source', 'TableLabelMe'),
                    'fallback': True  # 标记为回退数据
                }
            }

            return [fallback_annotation]

        except Exception as e:
            self.logger.error(f"创建回退标注失败 {image_id}: {e}")
            return []

    def _generate_annotation_id(self, image_id: int, cell_ind: int) -> int:
        """
        生成全局唯一的标注ID（Bug修复版本）。

        Args:
            image_id (int): 图像ID
            cell_ind (int): 单元格索引

        Returns:
            int: 全局唯一的安全标注ID
        """
        try:
            # 简单的ID生成策略：image_id * 1000 + cell_ind
            annotation_id = image_id * 1000 + cell_ind

            # Bug修复：确保annotation_id在安全范围内
            MAX_SAFE_INT = 2147483647  # 2^31 - 1
            if annotation_id > MAX_SAFE_INT:
                # 如果超出范围，使用更安全的生成策略
                # 使用哈希组合而不是简单乘法
                import hashlib
                combined_str = f"{image_id}_{cell_ind}"
                hash_obj = hashlib.md5(combined_str.encode('utf-8'))
                annotation_id = int(hash_obj.hexdigest()[:8], 16) % MAX_SAFE_INT

                # self.logger.warning(f"🔧 annotation_id超出安全范围，使用哈希策略: "
                #                   f"img_id={image_id}, cell_ind={cell_ind} -> {annotation_id}")

            return annotation_id

        except Exception as e:
            self.logger.warning(f"❌ 标注ID生成失败: {e}")
            # 回退策略：使用简单的组合
            return (image_id % 1000000) * 1000 + (cell_ind % 1000)

    # ========== 步骤5.2：4个内置转换方法 ==========

    def _convert_bbox_to_segmentation(self, bbox: Dict[str, Dict[str, Union[int, float]]]) -> Optional[List[float]]:
        """
        将四个角点坐标转换为一维segmentation数组（步骤5.2）。

        Args:
            bbox (Dict): 包含p1-p4四个点的字典，格式：
                        {"p1": {"x": float, "y": float}, "p2": {...}, ...}

        Returns:
            Optional[List[float]]: [p1.x, p1.y, p2.x, p2.y, p3.x, p3.y, p4.x, p4.y]
                                  转换失败时返回None

        Note:
            复用迭代1的TableLabelMeParser转换逻辑，确保一致性。
        """
        try:
            # 提取四个角点坐标
            p1 = bbox['p1']
            p2 = bbox['p2']
            p3 = bbox['p3']
            p4 = bbox['p4']

            # 按顺序组合成segmentation数组
            segmentation = [
                float(p1['x']), float(p1['y']),
                float(p2['x']), float(p2['y']),
                float(p3['x']), float(p3['y']),
                float(p4['x']), float(p4['y'])
            ]

            return segmentation

        except (KeyError, TypeError, ValueError) as e:
            self.logger.warning(f"bbox转换失败: {e}")
            return None

    def _convert_lloc_to_logic_axis(self, lloc: Dict[str, Union[int, float]]) -> Optional[List[int]]:
        """
        将逻辑位置信息转换为logic_axis格式（步骤5.2 + Bug修复）。

        实现安全的数值范围限制和类型转换，防止PyTorch整数溢出错误。

        Args:
            lloc (Dict): 包含行列位置信息的字典，格式：
                        {"start_row": int, "end_row": int, "start_col": int, "end_col": int}

        Returns:
            Optional[List[int]]: [start_row, end_row, start_col, end_col]
                               转换失败时返回None

        Note:
            修复版本：添加数值范围检查，防止整数溢出，确保PyTorch兼容性。
        """
        try:
            # 定义安全的数值范围（PyTorch int32兼容）
            # 使用较保守的范围以确保在各种PyTorch版本中都能正常工作
            MIN_SAFE_VALUE = -2147483648  # int32最小值
            MAX_SAFE_VALUE = 2147483647   # int32最大值

            # 定义合理的表格逻辑坐标范围（基于实际表格结构）
            MIN_LOGICAL_VALUE = 0         # 逻辑坐标不应为负数
            MAX_LOGICAL_VALUE = 10000     # 合理的最大表格行列数

            # 提取原始值
            raw_values = {
                'start_row': lloc.get('start_row'),
                'end_row': lloc.get('end_row'),
                'start_col': lloc.get('start_col'),
                'end_col': lloc.get('end_col')
            }

            # 检查必需字段是否存在
            missing_fields = [k for k, v in raw_values.items() if v is None]
            if missing_fields:
                self.logger.warning(f"lloc缺少必需字段: {missing_fields}")
                return None

            # 安全转换和范围检查
            converted_values = []
            has_overflow = False
            has_unreasonable = False

            for field_name, raw_value in raw_values.items():
                try:
                    # 尝试转换为整数
                    int_value = int(float(raw_value))  # 先转float再转int，处理字符串数字

                    # 检查是否超出安全范围
                    if int_value < MIN_SAFE_VALUE or int_value > MAX_SAFE_VALUE:
                        self.logger.error(f"🚨 检测到整数溢出风险: {field_name}={int_value} "
                                        f"(安全范围: {MIN_SAFE_VALUE} ~ {MAX_SAFE_VALUE})")
                        has_overflow = True
                        # 截断到安全范围
                        int_value = max(MIN_SAFE_VALUE, min(MAX_SAFE_VALUE, int_value))
                        self.logger.warning(f"🔧 已截断到安全值: {field_name}={int_value}")

                    # 检查是否超出合理范围
                    if int_value < MIN_LOGICAL_VALUE or int_value > MAX_LOGICAL_VALUE:
                        self.logger.warning(f"⚠️  检测到不合理的逻辑坐标: {field_name}={int_value} "
                                          f"(合理范围: {MIN_LOGICAL_VALUE} ~ {MAX_LOGICAL_VALUE})")
                        has_unreasonable = True
                        # 截断到合理范围
                        int_value = max(MIN_LOGICAL_VALUE, min(MAX_LOGICAL_VALUE, int_value))
                        self.logger.warning(f"🔧 已截断到合理值: {field_name}={int_value}")

                    converted_values.append(int_value)

                except (ValueError, TypeError) as e:
                    self.logger.error(f"❌ 数值转换失败: {field_name}={raw_value}, 错误: {e}")
                    return None

            # 构建logic_axis数组
            logic_axis = converted_values

            # 逻辑一致性检查
            start_row, end_row, start_col, end_col = logic_axis
            if start_row > end_row:
                self.logger.warning(f"⚠️  逻辑坐标不一致: start_row({start_row}) > end_row({end_row})")
            if start_col > end_col:
                self.logger.warning(f"⚠️  逻辑坐标不一致: start_col({start_col}) > end_col({end_col})")

            # 记录处理结果
            if has_overflow:
                self.logger.error(f"🚨 已处理整数溢出问题: 原始值={raw_values}, 安全值={logic_axis}")
            elif has_unreasonable:
                self.logger.warning(f"⚠️  已处理不合理数值: 原始值={raw_values}, 合理值={logic_axis}")
            else:
                self.logger.debug(f"✅ logic_axis转换成功: {logic_axis}")

            return logic_axis

        except Exception as e:
            self.logger.error(f"❌ lloc转换发生未预期错误: {e}, 输入数据: {lloc}")
            return None

    def _generate_image_id(self, file_path: str) -> int:
        """
        基于文件路径哈希生成唯一ID（步骤5.2 + Bug修复）。

        Args:
            file_path (str): 文件路径

        Returns:
            int: 基于路径生成的安全唯一图像ID

        Note:
            修复版本：确保生成的ID在PyTorch安全范围内，防止整数溢出。
            使用与迭代1的TableLabelMeParser相同的ID生成策略。
        """
        try:
            # 使用文件路径的哈希值生成ID
            import hashlib
            path_hash = hashlib.md5(file_path.encode('utf-8')).hexdigest()
            # 取哈希值的前8位转换为整数（保持原逻辑）
            image_id = int(path_hash[:8], 16)

            # Bug修复：确保ID在安全范围内（32位有符号整数）
            MAX_SAFE_INT = 2147483647  # 2^31 - 1
            if image_id > MAX_SAFE_INT:
                # 如果超出范围，使用模运算确保在安全范围内
                original_id = image_id
                image_id = image_id % MAX_SAFE_INT
                # self.logger.warning(f"🔧 ID超出安全范围，已调整: {file_path}")
                # self.logger.warning(f"   原始ID: {original_id} -> 安全ID: {image_id}")

            # self.logger.debug(f"✅ 安全ID生成: {file_path} -> {image_id}")
            return image_id

        except Exception as e:
            self.logger.warning(f"❌ 图像ID生成失败: {e}")
            # 回退到简单的哈希策略，确保安全范围
            fallback_id = abs(hash(file_path)) % 1000000000  # 限制在10亿以内
            self.logger.warning(f"🔧 使用回退ID: {fallback_id}")
            return fallback_id

    def _calculate_area(self, segmentation: List[float]) -> float:
        """
        根据segmentation坐标计算单元格面积（步骤5.2）。

        Args:
            segmentation (List[float]): 8个坐标值的数组 [x1,y1,x2,y2,x3,y3,x4,y4]

        Returns:
            float: 计算得出的面积

        Note:
            使用鞋带公式计算多边形面积，复用迭代1的TableLabelMeParser逻辑。
        """
        if len(segmentation) != 8:
            return 0.0

        try:
            # 提取坐标点
            x = [segmentation[i] for i in range(0, 8, 2)]
            y = [segmentation[i] for i in range(1, 8, 2)]

            # 使用鞋带公式计算面积
            area = 0.0
            n = len(x)
            for i in range(n):
                j = (i + 1) % n
                area += x[i] * y[j]
                area -= x[j] * y[i]

            return abs(area) / 2.0

        except (TypeError, ValueError, IndexError) as e:
            self.logger.warning(f"面积计算失败: {e}")
            return 0.0

    def _convert_to_lore_format(self, parsed_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        将TableLabelMe格式的标注转换为LORE-TSR内部格式（步骤5.2核心方法）。

        Args:
            parsed_data (Dict[str, Any]): 解析后的TableLabelMe标注数据，包含：
                - bbox: 四个角点坐标
                - lloc: 逻辑位置信息
                - cell_ind: 单元格索引
                - image_path: 图像文件路径
                - 其他额外信息

        Returns:
            Optional[Dict[str, Any]]: LORE-TSR格式的标注数据，失败时返回None

        Note:
            这是步骤5.2的核心方法，集成所有内置转换方法，
            实现完整的TableLabelMe → LORE-TSR格式转换流程。
        """
        try:
            self.logger.debug(f"开始转换标注数据: {parsed_data.get('cell_ind', 'unknown')}")

            # 步骤1：验证输入数据
            required_fields = ['bbox', 'lloc', 'cell_ind', 'image_path']
            missing_fields = [field for field in required_fields if field not in parsed_data]
            if missing_fields:
                self.logger.warning(f"缺少必需字段: {missing_fields}")
                return None

            # 步骤2：提取bbox.p1-p4坐标并转换为segmentation格式
            segmentation = self._convert_bbox_to_segmentation(parsed_data['bbox'])
            if segmentation is None:
                self.logger.warning("bbox转换失败")
                return None

            # 步骤3：提取lloc逻辑坐标并转换为logic_axis格式
            logic_axis = self._convert_lloc_to_logic_axis(parsed_data['lloc'])
            if logic_axis is None:
                self.logger.warning("lloc转换失败")
                return None

            # 步骤4：基于文件路径生成image_id
            image_id = self._generate_image_id(parsed_data['image_path'])

            # 步骤5：计算area和bbox
            area = self._calculate_area(segmentation)
            bbox = self._extract_bbox_from_segmentation(segmentation)

            # 步骤6：生成全局唯一的标注ID
            annotation_id = self._generate_annotation_id(image_id, parsed_data['cell_ind'])

            # 步骤7：构建LORE-TSR格式数据
            lore_annotation = {
                'id': annotation_id,
                'image_id': image_id,
                'category_id': 1,  # 固定为1（单元格类别）
                'segmentation': [segmentation],
                'logic_axis': [logic_axis],
                'area': area,
                'bbox': bbox,
                'iscrowd': 0,
                'ignore': 0,
                'extra_info': {
                    'table_ind': parsed_data.get('table_ind', 0),
                    'type': parsed_data.get('type', 'cell'),
                    'border': parsed_data.get('border', True),
                    'content': parsed_data.get('content', ''),
                    'quality': parsed_data.get('quality', '合格'),
                    'annotation_path': parsed_data.get('annotation_path', ''),
                    'part_dir': parsed_data.get('part_dir', ''),
                    'dataset_source': parsed_data.get('dataset_source', 'TableLabelMe')
                }
            }

            # 步骤8：验证转换结果
            if self._validate_lore_annotation(lore_annotation):
                self.logger.debug(f"标注转换成功: {annotation_id}")
                return lore_annotation
            else:
                self.logger.warning(f"转换结果验证失败: {annotation_id}")
                return None

        except Exception as e:
            self.logger.error(f"数据转换异常: {e}")
            return None

    def _extract_bbox_from_segmentation(self, segmentation: List[float]) -> List[float]:
        """
        从segmentation坐标提取bbox格式。

        Args:
            segmentation (List[float]): 8个坐标值的数组

        Returns:
            List[float]: [x, y, width, height]格式的bbox
        """
        if len(segmentation) != 8:
            return [0.0, 0.0, 0.0, 0.0]

        try:
            # 提取所有x和y坐标
            x_coords = [segmentation[i] for i in range(0, 8, 2)]
            y_coords = [segmentation[i] for i in range(1, 8, 2)]

            # 计算边界框
            min_x = min(x_coords)
            max_x = max(x_coords)
            min_y = min(y_coords)
            max_y = max(y_coords)

            # 返回[x, y, width, height]格式
            return [min_x, min_y, max_x - min_x, max_y - min_y]

        except (TypeError, ValueError, IndexError):
            return [0.0, 0.0, 0.0, 0.0]

    def _validate_lore_annotation(self, annotation: Dict[str, Any]) -> bool:
        """
        验证转换后的LORE-TSR格式标注数据。

        Args:
            annotation (Dict[str, Any]): 转换后的标注数据

        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查必需字段
            required_fields = ['id', 'image_id', 'category_id', 'segmentation', 'logic_axis', 'area', 'bbox']
            for field in required_fields:
                if field not in annotation:
                    return False

            # 检查数据类型和格式
            if not isinstance(annotation['segmentation'], list) or len(annotation['segmentation'][0]) != 8:
                return False

            if not isinstance(annotation['logic_axis'], list) or len(annotation['logic_axis'][0]) != 4:
                return False

            if not isinstance(annotation['bbox'], list) or len(annotation['bbox']) != 4:
                return False

            if not isinstance(annotation['area'], (int, float)) or annotation['area'] < 0:
                return False

            return True

        except Exception:
            return False

    def _build_file_index(self) -> Dict[int, Dict[str, str]]:
        """
        构建文件索引并进行质量筛选。

        Returns:
            Dict[int, Dict[str, str]]: 经过质量筛选的文件映射字典

        Note:
            集成质量筛选功能，在文件扫描后进行质量筛选，
            确保只加载符合质量要求的数据样本。
        """
        try:
            # 获取数据路径配置
            data_paths = self._get_data_paths()

            if not data_paths:
                self.logger.warning(f'未找到{self.split}分割的数据路径配置')
                return {}

            # 使用FileScanner进行目录扫描
            scan_result = self.file_scanner.scan_directories(data_paths, self.split)

            # 保存扫描统计信息
            self.scan_statistics = scan_result.get('statistics', {})

            # 获取原始文件索引
            raw_file_index = scan_result.get('file_index', {})

            self.logger.info(f'原始文件扫描完成，发现 {len(raw_file_index)} 个文件对')
            if hasattr(self, 'scan_statistics'):
                stats = self.scan_statistics
                self.logger.info(f'扫描统计：{stats.get("valid_pairs", 0)}个有效对，'
                              f'{stats.get("orphan_images", 0)}个孤儿图像，'
                              f'扫描时间：{stats.get("scan_time", 0):.3f}秒')

            # 进行质量筛选
            filter_result = self.quality_filter.filter_samples(raw_file_index, self.split)

            # 保存筛选结果和统计信息
            self.filter_statistics = filter_result["statistics"]
            self.exception_report = filter_result["exception_report"]

            # 记录筛选结果
            stats = self.filter_statistics
            self.logger.info(f'质量筛选完成 - 有效: {stats["valid_samples"]}, '
                            f'筛选掉: {stats["filtered_samples"]}, '
                            f'错误: {stats["error_samples"]}, '
                            f'成功率: {self.exception_report["summary"]["success_rate"]:.1f}%')

            return filter_result["filtered_index"]

        except Exception as e:
            self.logger.error(f'文件索引构建失败：{e}')
            self.logger.info('回退到空索引')
            # 保存错误信息到统计中
            self.scan_statistics = {
                'error': str(e),
                'valid_pairs': 0,
                'total_images': 0,
                'total_annotations': 0
            }
            self.filter_statistics = {
                'total_processed': 0,
                'valid_samples': 0,
                'filtered_samples': 0,
                'error_samples': 0
            }
            self.exception_report = {
                'summary': {'success_rate': 0.0}
            }
            return {}

    def get_quality_report(self) -> Dict[str, Any]:
        """
        获取质量筛选报告。

        Returns:
            Dict[str, Any]: 包含统计信息和异常报告的字典
        """
        return {
            "statistics": getattr(self, 'filter_statistics', {}),
            "exception_report": getattr(self, 'exception_report', {}),
            "scan_statistics": getattr(self, 'scan_statistics', {})
        }

    def __len__(self) -> int:
        """
        返回数据集大小（步骤5.1核心方法）。

        Returns:
            int: 数据集中的样本数量

        Note:
            这是步骤5.1要求实现的核心方法之一。
        """
        return self.num_samples

    # ========== 重要说明（步骤5.5修改） ==========
    #
    # 本类不再提供__getitem__方法及相关的数据处理逻辑。
    #
    # 架构设计：
    # - 本类（Table_labelmev2）：专注于提供COCO API兼容的数据源接口
    # - TableLabelMeCTDetDataset类：通过多重继承提供完整的数据处理逻辑
    # - 组合类：class TableLabelMeDataset(Table_labelmev2, TableLabelMeCTDetDataset)
    #
    # 优势：
    # 1. 解决BUG：获得完整的cc_match、st、mk_ind、mk_mask等字段支持
    # 2. 代码复用：100%复用CTDetDataset的成熟数据处理逻辑
    # 3. 架构清晰：数据源与数据处理逻辑分离，提高可维护性
    # 4. 设计一致：符合原有的多重继承设计理念
    #
    # ========== 数据处理逻辑移除（步骤5.5） ==========



    # ========== COCO API兼容接口（步骤5.1基础框架） ==========

    def getImgIds(self) -> List[int]:
        """
        获取所有图像ID列表，兼容COCO API接口。

        Returns:
            List[int]: 图像ID列表
        """
        return self.images

    def loadImgs(self, ids: List[int]) -> List[Dict[str, Any]]:
        """
        加载指定ID的图像信息，兼容COCO API接口。

        Args:
            ids (List[int]): 图像ID列表

        Returns:
            List[Dict[str, Any]]: 图像信息列表
        """
        return [self.image_info[img_id] for img_id in ids if img_id in self.image_info]

    def getAnnIds(self, imgIds: List[int]) -> List[int]:
        """
        获取指定图像的标注ID列表，兼容COCO API接口。

        Args:
            imgIds (List[int]): 图像ID列表

        Returns:
            List[int]: 标注ID列表
        """
        ann_ids = []
        for img_id in imgIds:
            if img_id in self.annotations:
                for ann in self.annotations[img_id]:
                    ann_ids.append(ann['id'])
        return ann_ids

    def loadAnns(self, ids: List[int]) -> List[Dict[str, Any]]:
        """
        加载指定ID的标注信息，兼容COCO API接口。

        Args:
            ids (List[int]): 标注ID列表

        Returns:
            List[Dict[str, Any]]: 标注信息列表
        """
        annotations = []
        for img_id in self.annotations:
            for ann in self.annotations[img_id]:
                if ann['id'] in ids:
                    annotations.append(ann)
        return annotations

    def _to_float(self, x: Union[int, float]) -> float:
        """
        数值转换为浮点数，保留两位小数。

        Args:
            x (Union[int, float]): 输入数值

        Returns:
            float: 格式化后的浮点数
        """
        return float("{:.2f}".format(x))

    def convert_eval_format(self, all_bboxes: Dict, thresh: float) -> List[Dict[str, Any]]:
        """
        转换评估格式，兼容原Table类接口。

        Args:
            all_bboxes (Dict): 所有边界框预测结果
            thresh (float): 置信度阈值

        Returns:
            List[Dict[str, Any]]: 转换后的检测结果
        """
        detections = []
        for image_id in all_bboxes:
            for cls_ind in all_bboxes[image_id]:
                category_id = self._valid_ids[cls_ind - 1]
                for bbox in all_bboxes[image_id][cls_ind]:
                    if bbox[4] > float(thresh):
                        bbox[2] -= bbox[0]
                        bbox[3] -= bbox[1]
                        score = bbox[4]
                        bbox_out = list(map(self._to_float, bbox[0:4]))

                        detection = {
                            "image_id": int(image_id),
                            "category_id": int(category_id),
                            "bbox": bbox_out,
                            "score": float("{:.2f}".format(score))
                        }
                        if len(bbox) > 5:
                            extreme_points = list(map(self._to_float, bbox[5:13]))
                            detection["extreme_points"] = extreme_points
                        detections.append(detection)
        print('total:', len(detections))
        return detections

    def save_results(self, results: Dict, save_dir: str, thresh: float):
        """
        保存评估结果，兼容原Table类接口。

        Args:
            results (Dict): 预测结果
            save_dir (str): 保存目录
            thresh (float): 置信度阈值
        """
        json.dump(self.convert_eval_format(results, thresh),
                 open('{}/results.json'.format(save_dir), 'w'))

    def run_eval(self, results: Dict, save_dir: str, thresh: float):
        """
        运行评估，兼容原Table类接口。

        Args:
            results (Dict): 预测结果
            save_dir (str): 保存目录
            thresh (float): 置信度阈值

        Note:
            MVP版本暂不实现完整的COCO评估，仅保存结果。
            完整的评估功能将在后续迭代中实现。
        """
        self.save_results(results, save_dir, thresh)
        print('TableLabelMe评估结果已保存到: {}/results.json'.format(save_dir))
        print('注意：MVP版本暂不支持完整的COCO评估指标计算')
